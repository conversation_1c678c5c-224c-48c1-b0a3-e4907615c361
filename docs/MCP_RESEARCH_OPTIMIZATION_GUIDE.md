# 🔍 MCP Research Optimization Guide - Lessons Learned

## 🎯 Purpose

This guide documents lessons learned from the 2025-08-20 responsive UI session and provides optimized strategies for using our MCP tools more effectively for project-specific research.

## 📊 Session Analysis: What Went Wrong

### **❌ Research Issues Encountered**

#### **1. Generic Documentation Searches**
**What I Did Wrong:**
```
perform_rag_query("responsive design full screen layout mobile sidebar collapsible")
```
**Result**: Got Solidity documentation and unrelated content instead of React/Next.js patterns

#### **2. Broad GitHub Searches**
**What I Did Wrong:**
```
searchGitHub_grep("h-screen w-screen bg-gray")
```
**Result**: Found random repositories instead of our specific tech stack patterns

#### **3. Missed Project-Specific Context**
**What I Did Wrong:**
- Didn't search our own codebase first
- Didn't leverage our existing patterns
- Didn't use project-specific terminology

## ✅ Optimized MCP Research Strategy

### **🎯 Phase 1: Project-First Research**

#### **1. Start with Our Own Codebase**
```typescript
// ALWAYS start here for any UI work
search_code_examples_archon({
  query: "React Next.js responsive sidebar Tailwind CSS",
  source_id: "our-project-specific-sources",
  match_count: 5
})
```

#### **2. Use Project-Specific Terminology**
```typescript
// Instead of generic terms, use our stack
perform_rag_query_archon({
  query: "Next.js 15 React 19 Tailwind CSS responsive design patterns",
  match_count: 3
})
```

### **🎯 Phase 2: Targeted External Research**

#### **1. Stack-Specific GitHub Searches**
```typescript
// Target our exact tech stack
searchGitHub_grep({
  query: "useState.*sidebar.*mobile",
  language: ["TypeScript", "TSX"],
  repo: "vercel/", // Target Next.js ecosystem
  useRegexp: true
})
```

#### **2. Library-Specific Documentation**
```typescript
// Get official docs for our exact libraries
get-library-docs_docfork({
  libraryName: "vercel/next.js",
  topic: "responsive design"
})
```

## 🛠️ Tool-Specific Optimization Strategies

### **🏗️ Archon MCP Tools - Our Primary Knowledge Base**

#### **Best Practices:**
1. **Use Specific Technical Terms**
   ```typescript
   // ✅ GOOD
   perform_rag_query("Next.js responsive breakpoints Tailwind CSS mobile-first")
   
   // ❌ BAD
   perform_rag_query("responsive design mobile")
   ```

2. **Search Our Code Examples First**
   ```typescript
   // ✅ ALWAYS start here
   search_code_examples_archon({
     query: "React hooks useState sidebar state management",
     match_count: 3
   })
   ```

3. **Check Available Sources**
   ```typescript
   // ✅ Know what we have
   get_available_sources_archon()
   ```

### **🐙 GitHub MCP Tools - External Pattern Research**

#### **Optimized Search Patterns:**

1. **Repository-Specific Searches**
   ```typescript
   // ✅ Target quality repositories
   githubSearchCode_octocode([{
     queryTerms: ["responsive", "sidebar"],
     owner: ["vercel", "shadcn-ui", "tailwindlabs"],
     language: "typescript",
     stars: ">1000"
   }])
   ```

2. **Pattern-Specific Searches**
   ```typescript
   // ✅ Search for exact patterns we need
   searchGitHub_grep({
     query: "lg:hidden.*sidebar.*mobile",
     language: ["TSX"],
     repo: "shadcn-ui/",
     useRegexp: true
   })
   ```

3. **Package Research**
   ```typescript
   // ✅ Research packages for our stack
   packageSearch_octocode({
     npmPackages: [{
       name: "tailwindcss responsive",
       searchLimit: 3
     }]
   })
   ```

### **📚 Forkdocs MCP Tools - Official Documentation**

#### **Strategic Usage:**
```typescript
// ✅ Get authoritative docs for our exact stack
get-library-docs_docfork("tailwindcss", "responsive-design")
get-library-docs_docfork("react", "hooks")
get-library-docs_docfork("next.js", "app-router")
```

## 🎯 Project-Specific Research Workflow

### **For UI/Frontend Tasks:**

1. **Check Our Patterns First**
   ```typescript
   search_code_examples_archon({
     query: "React component responsive design Tailwind",
     source_id: "our-ui-patterns"
   })
   ```

2. **Research Our Tech Stack**
   ```typescript
   perform_rag_query_archon({
     query: "Next.js 15 responsive design best practices",
     match_count: 3
   })
   ```

3. **Find External Examples**
   ```typescript
   githubSearchCode_octocode([{
     queryTerms: ["responsive", "mobile"],
     owner: ["vercel", "shadcn-ui"],
     language: "typescript"
   }])
   ```

4. **Get Official Guidance**
   ```typescript
   get-library-docs_docfork("tailwindcss", "responsive-design")
   ```

### **For Backend/MCP Tasks:**

1. **Check Our MCP Integration**
   ```typescript
   search_code_examples_archon({
     query: "MCP client configuration TypeScript",
     source_id: "our-mcp-examples"
   })
   ```

2. **Research MCP Patterns**
   ```typescript
   perform_rag_query_archon({
     query: "Model Context Protocol server integration",
     match_count: 5
   })
   ```

3. **Find MCP Examples**
   ```typescript
   githubSearchCode_octocode([{
     queryTerms: ["mcp-use", "ModelContextProtocol"],
     language: "typescript"
   }])
   ```

## 🚀 Advanced Research Techniques

### **1. Progressive Query Refinement**
```typescript
// Start broad, then narrow
const queries = [
  { queryTerms: ["responsive"] },
  { queryTerms: ["responsive", "sidebar"] },
  { queryTerms: ["responsive", "sidebar", "mobile"] },
  { queryTerms: ["responsive", "sidebar", "mobile", "tailwind"] }
];
```

### **2. Multi-Source Validation**
```typescript
// Cross-reference multiple sources
const archonResults = await perform_rag_query_archon("Next.js responsive");
const githubResults = await searchGitHub_grep("Next.js responsive");
const docsResults = await get-library-docs_docfork("next.js", "responsive");
```

### **3. Context-Aware Searches**
```typescript
// Use project context in queries
perform_rag_query_archon({
  query: `${currentProjectStack} responsive design patterns`,
  match_count: 5
})
```

## 📋 Research Quality Checklist

### **Before Starting Research:**
- [ ] Identified specific technical requirements
- [ ] Listed our exact tech stack components
- [ ] Checked what sources are available in Archon
- [ ] Planned progressive query strategy

### **During Research:**
- [ ] Started with project-specific sources
- [ ] Used exact library/framework names
- [ ] Targeted quality repositories (high stars)
- [ ] Cross-referenced multiple sources
- [ ] Validated patterns against our architecture

### **After Research:**
- [ ] Found patterns specific to our tech stack
- [ ] Validated implementation approaches
- [ ] Documented research findings
- [ ] Created implementation plan based on research

## 🎯 Key Takeaways for Future Sessions

### **✅ DO:**
1. **Start with our own knowledge base** - Use Archon tools first
2. **Be specific with tech stack** - Use exact library names and versions
3. **Target quality sources** - High-star repos, official docs
4. **Progressive refinement** - Start broad, narrow down
5. **Cross-validate** - Use multiple sources for confirmation

### **❌ DON'T:**
1. **Use generic search terms** - Avoid vague queries
2. **Ignore project context** - Always consider our specific stack
3. **Skip our own patterns** - Check existing code first
4. **Accept irrelevant results** - Refine queries if results are off-topic
5. **Rush the research phase** - Proper research saves implementation time

## 🔄 Continuous Improvement

### **After Each Session:**
1. **Document what worked** - Note successful query patterns
2. **Identify gaps** - What sources were missing?
3. **Update this guide** - Add new optimization strategies
4. **Share learnings** - Update team knowledge base

---

**📝 Created**: 2025-08-20  
**📊 Based on**: Responsive UI implementation session analysis  
**🎯 Purpose**: Optimize MCP tool usage for project-specific research  
**🔄 Status**: Living document - update after each research session

## 📚 Appendix: Session-Specific Examples

### **What I Should Have Done in the Responsive UI Session**

#### **❌ What I Actually Did:**
```typescript
// Generic search that returned Solidity docs
perform_rag_query_archon({
  query: "responsive design full screen layout mobile sidebar collapsible",
  match_count: 5
})
```

#### **✅ What I Should Have Done:**
```typescript
// 1. Check our existing UI patterns first
search_code_examples_archon({
  query: "React Next.js sidebar component state management",
  source_id: "ui.shadcn.com", // We had shadcn examples available!
  match_count: 3
})

// 2. Search for our specific tech stack
perform_rag_query_archon({
  query: "Next.js 15 Tailwind CSS responsive sidebar mobile breakpoints",
  match_count: 3
})

// 3. Target quality React/Next.js repositories
githubSearchCode_octocode([{
  queryTerms: ["useState", "sidebar", "responsive"],
  owner: ["vercel", "shadcn-ui", "tailwindlabs"],
  language: "typescript",
  stars: ">500"
}])

// 4. Get official Tailwind responsive docs
get-library-docs_docfork("tailwindcss", "responsive-design")
```

### **Discovered: We Had Great Sources Available!**

From the session, I found we actually had excellent sources:
- **shadcn/ui examples** - Perfect responsive sidebar patterns
- **Tailwind documentation** - Comprehensive responsive design guides
- **Next.js patterns** - Official responsive design recommendations

**Lesson**: Always check `get_available_sources_archon()` first!

## 🎯 Advanced MCP Research Patterns

### **Pattern 1: Stack-Specific Research Chain**
```typescript
// For any React/Next.js UI task
const researchChain = async () => {
  // 1. Our patterns
  const ourPatterns = await search_code_examples_archon({
    query: "React component responsive design",
    source_id: "ui.shadcn.com"
  });

  // 2. Official docs
  const officialDocs = await get-library-docs_docfork("react", "hooks");

  // 3. Quality examples
  const examples = await githubSearchCode_octocode([{
    queryTerms: ["useState", "responsive"],
    owner: ["vercel"],
    language: "typescript"
  }]);

  return { ourPatterns, officialDocs, examples };
};
```

### **Pattern 2: Progressive Specificity**
```typescript
// Start general, get specific
const progressiveSearch = async (baseQuery: string) => {
  const queries = [
    `${baseQuery}`,
    `${baseQuery} React`,
    `${baseQuery} React Next.js`,
    `${baseQuery} React Next.js Tailwind CSS`,
    `${baseQuery} React Next.js Tailwind CSS TypeScript`
  ];

  for (const query of queries) {
    const results = await perform_rag_query_archon({ query, match_count: 2 });
    if (results.success && results.results.length > 0) {
      return results; // Found relevant results
    }
  }
};
```

### **Pattern 3: Multi-Source Validation**
```typescript
// Validate patterns across sources
const validatePattern = async (pattern: string) => {
  const sources = await Promise.all([
    search_code_examples_archon({ query: pattern }),
    githubSearchCode_octocode([{ queryTerms: [pattern] }]),
    get-library-docs_docfork("react", pattern)
  ]);

  // Cross-reference results for consistency
  return sources.filter(source => source.success);
};
```

## 🔧 Tool-Specific Optimization Tips

### **Archon MCP Optimization**
```typescript
// ✅ Use technical terminology from our stack
perform_rag_query_archon({
  query: "TypeScript React hooks useState useEffect responsive design",
  match_count: 3
})

// ✅ Search our code examples with specific patterns
search_code_examples_archon({
  query: "sidebar collapsible mobile overlay backdrop",
  source_id: "ui.shadcn.com",
  match_count: 5
})
```

### **GitHub MCP Optimization**
```typescript
// ✅ Target our ecosystem
githubSearchCode_octocode([{
  queryTerms: ["responsive", "sidebar"],
  owner: ["vercel", "shadcn-ui", "tailwindlabs", "chakra-ui"],
  language: "typescript",
  stars: ">100",
  pushed: ">2023-01-01" // Recent patterns
}])

// ✅ Use regex for specific patterns
searchGitHub_grep({
  query: "useState.*sidebar.*mobile",
  language: ["TypeScript", "TSX"],
  useRegexp: true,
  repo: "shadcn-ui/"
})
```

### **Forkdocs MCP Optimization**
```typescript
// ✅ Get docs for our exact stack
const stackDocs = await Promise.all([
  get-library-docs_docfork("next.js", "responsive-design"),
  get-library-docs_docfork("tailwindcss", "responsive-design"),
  get-library-docs_docfork("react", "hooks")
]);
```

## 📊 Research Quality Metrics

### **Success Indicators:**
- ✅ Found patterns specific to our tech stack
- ✅ Got examples from quality sources (shadcn, Vercel, etc.)
- ✅ Cross-validated approaches across multiple sources
- ✅ Found official documentation for our libraries
- ✅ Discovered implementation patterns we can adapt

### **Failure Indicators:**
- ❌ Generic results not related to our stack
- ❌ Outdated patterns or deprecated approaches
- ❌ Single-source validation without cross-reference
- ❌ Missing official documentation
- ❌ Patterns that don't fit our architecture

---

**🎯 Next Update**: After next research-heavy session, add new patterns and optimizations discovered.
